Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_a8572/fast && /usr/bin/make -f CMakeFiles/cmTC_a8572.dir/build.make CMakeFiles/cmTC_a8572.dir/build
make[1]: Entering directory '/home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_a8572.dir/src.c.o
/usr/bin/gcc-8   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_a8572.dir/src.c.o   -c /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_a8572
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a8572.dir/link.txt --verbose=1
/usr/bin/gcc-8  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_a8572.dir/src.c.o  -o cmTC_a8572 
/usr/bin/ld: CMakeFiles/cmTC_a8572.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_a8572.dir/build.make:87: cmTC_a8572] Error 1
make[1]: Leaving directory '/home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_a8572/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_0253e/fast && /usr/bin/make -f CMakeFiles/cmTC_0253e.dir/build.make CMakeFiles/cmTC_0253e.dir/build
make[1]: Entering directory '/home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_0253e.dir/CheckFunctionExists.c.o
/usr/bin/gcc-8   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_0253e.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_0253e
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0253e.dir/link.txt --verbose=1
/usr/bin/gcc-8  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_0253e.dir/CheckFunctionExists.c.o  -o cmTC_0253e  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_0253e.dir/build.make:87: cmTC_0253e] Error 1
make[1]: Leaving directory '/home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_0253e/fast] Error 2



