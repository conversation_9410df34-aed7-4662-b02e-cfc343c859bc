# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Produce verbose output by default.
VERBOSE = 1

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@echo "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@echo "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all:
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp/CMakeFiles /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/weednix_ws/src/build/CMakeFiles/CMakeTmp/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

#=============================================================================
# Target rules for targets named cmTC_35f6f

# Build rule for target.
cmTC_35f6f:
	$(MAKE) -f CMakeFiles/Makefile2 cmTC_35f6f
.PHONY : cmTC_35f6f

# fast build rule for target.
cmTC_35f6f/fast:
	$(MAKE) -f CMakeFiles/cmTC_35f6f.dir/build.make CMakeFiles/cmTC_35f6f.dir/build
.PHONY : cmTC_35f6f/fast

CheckFunctionExists.o: CheckFunctionExists.c.o

.PHONY : CheckFunctionExists.o

# target to build an object file
CheckFunctionExists.c.o:
	$(MAKE) -f CMakeFiles/cmTC_35f6f.dir/build.make CMakeFiles/cmTC_35f6f.dir/CheckFunctionExists.c.o
.PHONY : CheckFunctionExists.c.o

CheckFunctionExists.i: CheckFunctionExists.c.i

.PHONY : CheckFunctionExists.i

# target to preprocess a source file
CheckFunctionExists.c.i:
	$(MAKE) -f CMakeFiles/cmTC_35f6f.dir/build.make CMakeFiles/cmTC_35f6f.dir/CheckFunctionExists.c.i
.PHONY : CheckFunctionExists.c.i

CheckFunctionExists.s: CheckFunctionExists.c.s

.PHONY : CheckFunctionExists.s

# target to generate assembly for a file
CheckFunctionExists.c.s:
	$(MAKE) -f CMakeFiles/cmTC_35f6f.dir/build.make CMakeFiles/cmTC_35f6f.dir/CheckFunctionExists.c.s
.PHONY : CheckFunctionExists.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... cmTC_35f6f"
	@echo "... CheckFunctionExists.o"
	@echo "... CheckFunctionExists.i"
	@echo "... CheckFunctionExists.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

