# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/weednix_ws/src/weednix_sensors/msg/WeedingStatus.msg"
services_str = ""
pkg_name = "weednix_sensors"
dependencies_str = "std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "weednix_sensors;/home/<USER>/weednix_ws/src/weednix_sensors/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
