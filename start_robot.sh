#!/bin/bash

# Weednix Real Robot Startup Script
# This script starts the real robot launch file, ROS bridge, and web interface

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WORKSPACE_DIR="$HOME/weednix_ws"
WEB_APP_DIR="$WORKSPACE_DIR/src/react-ros-app"
LOG_DIR="$WORKSPACE_DIR/logs"
LAUNCH_FILE="device1_bringup.launch"
LAUNCH_PACKAGE="weednix_launch"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to get IP address with multiple fallbacks
get_robot_ip() {
    local ip=""

    # Method 1: hostname -I (most reliable on Ubuntu)
    ip=$(hostname -I 2>/dev/null | awk '{print $1}' | grep -E '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$')

    # Method 2: ip route (works on most Linux systems)
    if [ -z "$ip" ]; then
        ip=$(ip route get ******* 2>/dev/null | awk '{print $7}' | head -1 | grep -E '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$')
    fi

    # Method 3: ip addr show (more detailed)
    if [ -z "$ip" ]; then
        ip=$(ip addr show 2>/dev/null | grep -E 'inet [0-9]' | grep -v '127.0.0.1' | awk '{print $2}' | cut -d'/' -f1 | head -1)
    fi

    # Method 4: ifconfig fallback
    if [ -z "$ip" ]; then
        ip=$(ifconfig 2>/dev/null | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi

    # Method 5: Check specific network interfaces
    if [ -z "$ip" ]; then
        for interface in eth0 wlan0 enp0s3 wlp2s0; do
            ip=$(ip addr show "$interface" 2>/dev/null | grep -E 'inet [0-9]' | awk '{print $2}' | cut -d'/' -f1 | head -1)
            [ -n "$ip" ] && break
        done
    fi

    echo "$ip"
}

# Function to update web app environment with current IP
update_web_app_ip() {
    local current_ip="$1"

    if [ -n "$current_ip" ]; then
        print_status "Updating web app configuration with IP: $current_ip"

        cd "$WEB_APP_DIR"

        # Update .env file with current IP
        cat > .env << EOF
# React App Configuration for Network Access (Auto-generated)
HOST=0.0.0.0
PORT=3000

# ROS Bridge Configuration (Updated: $(date))
REACT_APP_DEFAULT_ROS_URL=ws://$current_ip:9090
REACT_APP_ROBOT_IP=$current_ip

# Network Configuration
REACT_APP_SHOW_NETWORK_INFO=true
EOF

        print_success "Web app configuration updated"
    else
        print_warning "Could not determine IP address for web app configuration"
    fi
}

# Function to check if a process is running
is_process_running() {
    pgrep -f "$1" > /dev/null 2>&1
}

# Function to validate IP address
validate_ip() {
    local ip="$1"
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking system prerequisites..."

    local errors=0

    # Check if ROS is installed
    if ! command -v roscore >/dev/null 2>&1; then
        print_error "ROS is not installed or not in PATH"
        errors=$((errors + 1))
    fi

    # Check if Node.js is installed
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed"
        errors=$((errors + 1))
    fi

    # Check if npm is installed
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed"
        errors=$((errors + 1))
    fi

    # Check workspace setup
    if [ ! -f "$WORKSPACE_DIR/devel/setup.bash" ]; then
        print_error "ROS workspace not built. Run 'cd $WORKSPACE_DIR && catkin_make'"
        errors=$((errors + 1))
    fi

    # Check if launch file exists
    if [ ! -f "$WORKSPACE_DIR/src/$LAUNCH_PACKAGE/launch/$LAUNCH_FILE" ]; then
        print_error "Launch file not found: $WORKSPACE_DIR/src/$LAUNCH_PACKAGE/launch/$LAUNCH_FILE"
        errors=$((errors + 1))
    fi

    # Check network connectivity
    if ! ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        print_warning "No internet connectivity detected (this may affect some features)"
    fi

    if [ $errors -gt 0 ]; then
        print_error "Found $errors critical errors. Please fix them before continuing."
        return 1
    fi

    print_success "All prerequisites check passed"
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name="$1"
    local check_command="$2"
    local max_wait="$3"
    local wait_time=0

    print_status "Waiting for $service_name to be ready..."

    while [ $wait_time -lt $max_wait ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            print_success "$service_name is ready"
            return 0
        fi
        sleep 1
        wait_time=$((wait_time + 1))

        # Show progress every 5 seconds
        if [ $((wait_time % 5)) -eq 0 ]; then
            print_status "Still waiting for $service_name... ($wait_time/${max_wait}s)"
        fi
    done

    print_error "$service_name failed to start within $max_wait seconds"
    return 1
}

# Function to kill existing processes
cleanup_processes() {
    print_status "Cleaning up existing processes..."

    # Kill all ROS-related processes
    print_status "Stopping ROS processes..."
    pkill -f "roslaunch.*$LAUNCH_FILE" 2>/dev/null || true
    pkill -f "roslaunch.*rosbridge_websocket" 2>/dev/null || true
    pkill -f "roslaunch.*visual_servoing" 2>/dev/null || true
    pkill -f "roslaunch.*kinect" 2>/dev/null || true
    pkill -f "roslaunch.*openni" 2>/dev/null || true
    pkill -f "roscore" 2>/dev/null || true
    pkill -f "rosmaster" 2>/dev/null || true
    pkill -f "rosout" 2>/dev/null || true

    # Kill hardware interface processes (Arduino, sensors, etc.)
    print_status "Stopping hardware interface processes..."
    pkill -f "arduino" 2>/dev/null || true
    pkill -f "serial_node" 2>/dev/null || true
    pkill -f "imu_node" 2>/dev/null || true
    pkill -f "openni_node" 2>/dev/null || true
    pkill -f "openni_camera" 2>/dev/null || true

    # Kill ROS bridge and related processes
    print_status "Stopping ROS bridge processes..."
    pkill -f "rosbridge" 2>/dev/null || true
    pkill -f "tornado" 2>/dev/null || true

    # Kill web server processes
    print_status "Stopping web server processes..."
    pkill -f "npm.*start" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
    pkill -f "node.*react-scripts" 2>/dev/null || true
    pkill -f "webpack" 2>/dev/null || true

    # Kill any remaining Node.js processes from the web app directory
    if [ -d "$WEB_APP_DIR" ]; then
        print_status "Stopping Node.js processes in web app directory..."
        lsof -ti:3000 2>/dev/null | xargs kill -9 2>/dev/null || true
        lsof -ti:3001 2>/dev/null | xargs kill -9 2>/dev/null || true
    fi

    # Kill any Python processes related to visual servoing
    print_status "Stopping visual servoing processes..."
    pkill -f "row_crop_follower.py" 2>/dev/null || true
    pkill -f "geofencing_node.py" 2>/dev/null || true
    pkill -f "burn_calibrated.py" 2>/dev/null || true

    # Clean up any remaining processes using the workspace
    print_status "Cleaning up workspace processes..."
    pkill -f "$WORKSPACE_DIR" 2>/dev/null || true

    # Remove PID files
    print_status "Cleaning up PID files..."
    rm -f "$LOG_DIR"/*.pid 2>/dev/null || true

    # Wait longer for all processes to terminate
    print_status "Waiting for processes to terminate..."
    sleep 5

    # Force kill any stubborn processes
    print_status "Force killing any remaining processes..."
    pkill -9 -f "roslaunch" 2>/dev/null || true
    pkill -9 -f "npm.*start" 2>/dev/null || true
    pkill -9 -f "openni" 2>/dev/null || true

    print_success "Process cleanup completed"
}

# Function to check hardware connections
check_hardware() {
    print_status "Checking hardware connections..."

    local warnings=0

    # Check for Arduino connection
    if [ ! -e "/dev/ttyUSB_arduino" ]; then
        print_warning "Arduino not found at /dev/ttyUSB_arduino"
        print_warning "Make sure Arduino is connected and USB rules are set up"
        print_warning "You may need to run: sudo usermod -a -G dialout \$USER"
        warnings=$((warnings + 1))
    else
        print_success "Arduino found at /dev/ttyUSB_arduino"
    fi

    # Check for IMU connection (if applicable)
    local usb_devices=$(ls /dev/ttyUSB* 2>/dev/null | wc -l)
    if [ "$usb_devices" -eq 0 ]; then
        print_warning "No USB devices found for sensors"
        warnings=$((warnings + 1))
    else
        print_success "Found $usb_devices USB device(s)"
    fi

    # Check camera devices
    local camera_devices=$(ls /dev/video* 2>/dev/null | wc -l)
    if [ "$camera_devices" -eq 0 ]; then
        print_warning "No camera devices found"
        warnings=$((warnings + 1))
    else
        print_success "Found $camera_devices camera device(s)"
    fi

    # Check if user is in dialout group
    if ! groups | grep -q dialout; then
        print_warning "User not in dialout group. You may need to run:"
        print_warning "sudo usermod -a -G dialout \$USER && newgrp dialout"
        warnings=$((warnings + 1))
    fi

    if [ $warnings -gt 0 ]; then
        print_warning "Found $warnings hardware warnings. Robot may not function properly."
        print_status "Continue anyway? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_status "Startup cancelled by user"
            exit 1
        fi
    else
        print_success "All hardware checks passed"
    fi

    echo ""
}

# Function to start ROS launch file
start_ros_launch() {
    print_status "Starting ROS robot launch file..."

    cd "$WORKSPACE_DIR" || {
        print_error "Failed to change to workspace directory: $WORKSPACE_DIR"
        return 1
    }

    # Source ROS setup
    if [ -f "devel/setup.bash" ]; then
        source devel/setup.bash
    else
        print_error "ROS workspace setup file not found. Please build the workspace first."
        return 1
    fi

    # Check if roscore is running, start if needed
    if ! pgrep -f "roscore" >/dev/null 2>&1; then
        print_status "Starting roscore..."
        nohup roscore > "$LOG_DIR/roscore.log" 2>&1 &
        wait_for_service "ROS Master" "rostopic list" 10 || {
            print_error "Failed to start ROS Master"
            return 1
        }
    fi

    # Start the launch file in background
    print_status "Launching $LAUNCH_PACKAGE $LAUNCH_FILE..."
    nohup roslaunch "$LAUNCH_PACKAGE" "$LAUNCH_FILE" > "$LOG_DIR/robot_launch.log" 2>&1 &
    LAUNCH_PID=$!

    # Save PID immediately
    echo $LAUNCH_PID > "$LOG_DIR/robot_launch.pid"

    # Wait and verify the launch started successfully
    sleep 8
    if kill -0 $LAUNCH_PID 2>/dev/null; then
        # Additional check: verify some expected topics are published
        wait_for_service "Robot Topics" "rostopic list | grep -q '/cmd_vel'" 20 || {
            print_warning "Some robot topics may not be ready yet"
        }
        print_success "ROS robot launch started successfully (PID: $LAUNCH_PID)"
    else
        print_error "Failed to start ROS robot launch"
        print_error "Check log file: $LOG_DIR/robot_launch.log"
        return 1
    fi
}

# Function to start ROS bridge
start_ros_bridge() {
    print_status "Starting ROS bridge server..."

    cd "$WORKSPACE_DIR" || {
        print_error "Failed to change to workspace directory: $WORKSPACE_DIR"
        return 1
    }

    source devel/setup.bash

    # Check if rosbridge_server package is available
    if ! rospack find rosbridge_server >/dev/null 2>&1; then
        print_error "rosbridge_server package not found. Please install it:"
        print_error "sudo apt-get install ros-\$ROS_DISTRO-rosbridge-suite"
        return 1
    fi

    # Start ROS bridge in background
    nohup roslaunch rosbridge_server rosbridge_websocket.launch > "$LOG_DIR/rosbridge.log" 2>&1 &
    BRIDGE_PID=$!

    # Save PID immediately
    echo $BRIDGE_PID > "$LOG_DIR/rosbridge.pid"

    # Wait and verify the bridge started successfully
    sleep 3
    if kill -0 $BRIDGE_PID 2>/dev/null; then
        # Check if the websocket port is listening
        wait_for_service "ROS Bridge WebSocket" "ss -tlnp | grep -q ':9090'" 10 || {
            print_warning "ROS Bridge may not be fully ready yet"
        }
        print_success "ROS bridge server started successfully (PID: $BRIDGE_PID)"
    else
        print_error "Failed to start ROS bridge server"
        print_error "Check log file: $LOG_DIR/rosbridge.log"
        return 1
    fi
}

# Function to start web server
start_web_server() {
    print_status "Starting web server..."

    cd "$WEB_APP_DIR" || {
        print_error "Failed to change to web app directory: $WEB_APP_DIR"
        return 1
    }

    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in $WEB_APP_DIR"
        return 1
    fi

    # Check if node_modules exists and react-scripts is installed
    if [ ! -d "node_modules" ] || [ ! -f "node_modules/react-scripts/bin/react-scripts.js" ]; then
        print_warning "node_modules incomplete or react-scripts missing. Running npm install..."
        if ! npm install; then
            print_error "npm install failed"
            return 1
        fi

        # Verify react-scripts is now available
        if [ ! -f "node_modules/react-scripts/bin/react-scripts.js" ]; then
            print_error "react-scripts still not found after npm install"
            return 1
        fi
    fi

    # Get current IP and update configuration
    local current_ip=$(get_robot_ip)
    if [ -n "$current_ip" ] && validate_ip "$current_ip"; then
        update_web_app_ip "$current_ip"
    else
        print_warning "Could not determine valid IP address, using localhost"
    fi

    # Start the web server in background
    print_status "Starting React development server..."
    nohup bash -c "cd '$WEB_APP_DIR' && HOST=0.0.0.0 npm start" > "$LOG_DIR/webserver.log" 2>&1 &
    WEB_PID=$!

    # Save PID immediately
    echo $WEB_PID > "$LOG_DIR/webserver.pid"

    # Wait and verify the web server started successfully
    sleep 8
    if kill -0 $WEB_PID 2>/dev/null; then
        # Check if the web server port is listening
        wait_for_service "Web Server" "ss -tlnp | grep -q ':3000'" 20 || {
            print_warning "Web server may not be fully ready yet"
        }
        print_success "Web server started successfully (PID: $WEB_PID)"
    else
        print_error "Failed to start web server"
        print_error "Check log file: $LOG_DIR/webserver.log"
        return 1
    fi
}

# Function to display access information
show_access_info() {
    ROBOT_IP=$(get_robot_ip)
    
    echo ""
    echo "=========================================="
    echo -e "${GREEN}🤖 WEEDNIX ROBOT STARTED SUCCESSFULLY${NC}"
    echo "=========================================="
    echo ""
    echo -e "${BLUE}📡 Robot IP Address:${NC} $ROBOT_IP"
    echo ""
    echo -e "${BLUE}📱 Access URLs:${NC}"
    echo "  Local Access:     http://localhost:3000"
    echo "  Mobile Access:    http://$ROBOT_IP:3000"
    echo "  ROS Bridge:       ws://$ROBOT_IP:9090"
    echo ""
    echo -e "${BLUE}📋 What's Running:${NC}"
    echo "  ✅ Robot Hardware Interface"
    echo "  ✅ Sensor Fusion (EKF)"
    echo "  ✅ Visual Servoing"
    echo "  ✅ ROS Bridge Server"
    echo "  ✅ Web Interface"
    echo ""
    echo -e "${BLUE}📝 Log Files:${NC}"
    echo "  Robot Launch: $LOG_DIR/robot_launch.log"
    echo "  ROS Bridge:   $LOG_DIR/rosbridge.log"
    echo "  Web Server:   $LOG_DIR/webserver.log"
    echo ""
    echo -e "${YELLOW}💡 Tips:${NC}"
    echo "  - Connect your phone to the same WiFi network"
    echo "  - Open http://$ROBOT_IP:3000 on your phone"
    echo "  - Check hardware connections if issues occur"
    echo "  - Use 'pkill -f roslaunch' to stop ROS processes"
    echo "  - Use 'pkill -f npm' to stop web server"
    echo ""
}

# Main execution
main() {
    echo ""
    echo "=========================================="
    echo -e "${BLUE}🚀 STARTING WEEDNIX REAL ROBOT${NC}"
    echo "=========================================="
    echo ""

    # Check prerequisites first
    check_prerequisites || {
        print_error "Prerequisites check failed. Please fix the issues above."
        exit 1
    }

    # Get and validate IP address
    local robot_ip=$(get_robot_ip)
    if [ -n "$robot_ip" ] && validate_ip "$robot_ip"; then
        print_success "Detected IP address: $robot_ip"
    else
        print_warning "Could not detect valid IP address. Network features may be limited."
        robot_ip="localhost"
    fi

    # Check hardware connections
    check_hardware

    # Cleanup any existing processes
    cleanup_processes

    # Start services with error handling
    print_status "Starting services..."

    if ! start_ros_launch; then
        print_error "Failed to start ROS launch. Check logs and try again."
        cleanup_processes
        exit 1
    fi

    if ! start_ros_bridge; then
        print_error "Failed to start ROS bridge. Check logs and try again."
        cleanup_processes
        exit 1
    fi

    if ! start_web_server; then
        print_error "Failed to start web server. Check logs and try again."
        cleanup_processes
        exit 1
    fi

    # Show access information
    show_access_info

    print_success "All services started successfully!"
    print_status "Press Ctrl+C to stop all services"

    # Set up signal handlers for clean shutdown
    trap 'print_status "Shutting down..."; cleanup_processes; exit 0' INT TERM

    # Monitor services and restart if needed
    monitor_services
}

# Function to monitor services and restart if they crash
monitor_services() {
    print_status "Monitoring services... (Press Ctrl+C to stop)"

    local check_interval=10
    local restart_attempts=0
    local max_restart_attempts=3

    while true; do
        sleep $check_interval

        # Check each service
        local services_down=0

        # Check ROS launch
        if [ -f "$LOG_DIR/robot_launch.pid" ]; then
            local launch_pid=$(cat "$LOG_DIR/robot_launch.pid")
            if ! kill -0 "$launch_pid" 2>/dev/null; then
                print_warning "ROS launch process died (PID: $launch_pid)"
                services_down=$((services_down + 1))
            fi
        fi

        # Check ROS bridge
        if [ -f "$LOG_DIR/rosbridge.pid" ]; then
            local bridge_pid=$(cat "$LOG_DIR/rosbridge.pid")
            if ! kill -0 "$bridge_pid" 2>/dev/null; then
                print_warning "ROS bridge process died (PID: $bridge_pid)"
                services_down=$((services_down + 1))
            fi
        fi

        # Check web server
        if [ -f "$LOG_DIR/webserver.pid" ]; then
            local web_pid=$(cat "$LOG_DIR/webserver.pid")
            if ! kill -0 "$web_pid" 2>/dev/null; then
                print_warning "Web server process died (PID: $web_pid)"
                services_down=$((services_down + 1))
            fi
        fi

        # If services are down and we haven't exceeded restart attempts
        if [ $services_down -gt 0 ] && [ $restart_attempts -lt $max_restart_attempts ]; then
            restart_attempts=$((restart_attempts + 1))
            print_warning "Attempting to restart failed services (attempt $restart_attempts/$max_restart_attempts)"

            # Try to restart services
            cleanup_processes
            sleep 3

            start_ros_launch && start_ros_bridge && start_web_server || {
                print_error "Failed to restart services"
                continue
            }

            print_success "Services restarted successfully"

        elif [ $services_down -gt 0 ]; then
            print_error "Too many restart attempts. Please check the logs and restart manually."
            break
        fi

        # Reset restart attempts if all services are running
        if [ $services_down -eq 0 ]; then
            restart_attempts=0
        fi
    done
}

# Test mode for IP detection
if [ "$1" = "--test-ip" ]; then
    echo "🧪 Testing IP Detection..."
    echo "=========================="

    robot_ip=$(get_robot_ip)
    if [ -n "$robot_ip" ] && validate_ip "$robot_ip"; then
        echo "✅ IP Detection: $robot_ip"
        echo "✅ IP Validation: PASSED"
        echo ""
        echo "📱 Your mobile access URL would be:"
        echo "   http://$robot_ip:3000"
    else
        echo "❌ IP Detection: FAILED"
        echo "❌ Detected: '$robot_ip'"
    fi
    exit 0
fi

# Run main function
main "$@"
