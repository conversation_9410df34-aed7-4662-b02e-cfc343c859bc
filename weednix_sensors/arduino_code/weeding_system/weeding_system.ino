/*
 * Weeding System Arduino Code
 * 
 * This Arduino sketch controls servos and laser for the weeding system.
 * It communicates with the ROS system via serial commands.
 * 
 * Hardware connections:
 * - Servo X: Pin 10 (PWM)
 * - Servo Y: Pin 9 (PWM)
 * - Laser: Pin 8 (Digital Output)
 * - Status LED: Pin 13 (Built-in LED)
 * 
 * Serial Protocol:
 * Commands from ROS:
 * - "SX<angle>" - Set servo X to angle (70-120)
 * - "SY<angle>" - Set servo Y to angle (70-122)
 * - "L1" - Turn laser ON
 * - "L0" - Turn laser OFF
 * - "INIT" - Initialize system
 * - "STATUS" - Get system status
 * 
 * Responses to ROS:
 * - "OK" - Command executed successfully
 * - "ERROR:<message>" - Error occurred
 * - "STATUS:<servo_x>,<servo_y>,<laser_state>" - System status
 */

#include <Servo.h>

// Hardware pins
const int SERVO_X_PIN = 10;
const int SERVO_Y_PIN = 9;
const int LASER_PIN = 8;
const int STATUS_LED_PIN = 13;

// Servo objects
Servo servoX;
Servo servoY;

// Servo limits
const int SERVO_X_MIN = 70;
const int SERVO_X_MAX = 120;
const int SERVO_Y_MIN = 70;
const int SERVO_Y_MAX = 122;

// Current positions
int currentServoX = 88;  // Center position
int currentServoY = 85;  // Center position
bool laserState = false;

// Communication
String inputString = "";
bool stringComplete = false;

// Timing
unsigned long lastHeartbeat = 0;
const unsigned long HEARTBEAT_INTERVAL = 5000; // 5 seconds

void setup() {
  // Initialize serial communication
  Serial.begin(57600);
  
  // Initialize hardware
  servoX.attach(SERVO_X_PIN);
  servoY.attach(SERVO_Y_PIN);
  pinMode(LASER_PIN, OUTPUT);
  pinMode(STATUS_LED_PIN, OUTPUT);
  
  // Set initial positions
  servoX.write(currentServoX);
  servoY.write(currentServoY);
  digitalWrite(LASER_PIN, LOW);
  digitalWrite(STATUS_LED_PIN, HIGH);
  
  // Wait for servos to reach position
  delay(1000);
  
  // Reserve string buffer
  inputString.reserve(50);
  
  // Send ready signal
  Serial.println("READY:Weeding System Initialized");
  digitalWrite(STATUS_LED_PIN, LOW);
}

void loop() {
  // Handle serial commands
  if (stringComplete) {
    processCommand(inputString);
    inputString = "";
    stringComplete = false;
  }
  
  // Send periodic heartbeat
  unsigned long currentTime = millis();
  if (currentTime - lastHeartbeat > HEARTBEAT_INTERVAL) {
    sendHeartbeat();
    lastHeartbeat = currentTime;
  }
  
  // Blink status LED to show system is alive
  if ((currentTime / 500) % 2 == 0) {
    digitalWrite(STATUS_LED_PIN, HIGH);
  } else {
    digitalWrite(STATUS_LED_PIN, LOW);
  }
  
  delay(10); // Small delay to prevent overwhelming the system
}

void serialEvent() {
  while (Serial.available()) {
    char inChar = (char)Serial.read();
    
    if (inChar == '\n') {
      stringComplete = true;
    } else {
      inputString += inChar;
    }
  }
}

void processCommand(String command) {
  command.trim(); // Remove whitespace
  
  if (command.startsWith("SX")) {
    // Set servo X position
    int angle = command.substring(2).toInt();
    setServoX(angle);
    
  } else if (command.startsWith("SY")) {
    // Set servo Y position
    int angle = command.substring(2).toInt();
    setServoY(angle);
    
  } else if (command == "L1") {
    // Turn laser ON
    setLaser(true);
    
  } else if (command == "L0") {
    // Turn laser OFF
    setLaser(false);
    
  } else if (command == "INIT") {
    // Initialize system
    initializeSystem();
    
  } else if (command == "STATUS") {
    // Send status
    sendStatus();
    
  } else {
    // Unknown command
    Serial.println("ERROR:Unknown command: " + command);
  }
}

void setServoX(int angle) {
  if (angle >= SERVO_X_MIN && angle <= SERVO_X_MAX) {
    currentServoX = angle;
    servoX.write(angle);
    Serial.println("OK:ServoX=" + String(angle));
  } else {
    Serial.println("ERROR:ServoX angle out of range (" + String(SERVO_X_MIN) + "-" + String(SERVO_X_MAX) + ")");
  }
}

void setServoY(int angle) {
  if (angle >= SERVO_Y_MIN && angle <= SERVO_Y_MAX) {
    currentServoY = angle;
    servoY.write(angle);
    Serial.println("OK:ServoY=" + String(angle));
  } else {
    Serial.println("ERROR:ServoY angle out of range (" + String(SERVO_Y_MIN) + "-" + String(SERVO_Y_MAX) + ")");
  }
}

void setLaser(bool state) {
  laserState = state;
  digitalWrite(LASER_PIN, state ? HIGH : LOW);
  Serial.println("OK:Laser=" + String(state ? "ON" : "OFF"));
}

void initializeSystem() {
  // Reset to center positions
  currentServoX = 88;
  currentServoY = 85;
  laserState = false;
  
  servoX.write(currentServoX);
  servoY.write(currentServoY);
  digitalWrite(LASER_PIN, LOW);
  
  delay(500); // Wait for servos to move
  
  Serial.println("OK:System initialized");
}

void sendStatus() {
  String status = "STATUS:" + String(currentServoX) + "," + String(currentServoY) + "," + String(laserState ? 1 : 0);
  Serial.println(status);
}

void sendHeartbeat() {
  Serial.println("HEARTBEAT:" + String(millis()));
}
