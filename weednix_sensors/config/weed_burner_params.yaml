
# File: config/weed_burner_params.yaml
# Weed Burner Configuration Parameters

# Hardware Configuration
arduino_port: "/dev/ttyACM0"
camera_width: 640
camera_height: 480

# Detection Parameters
model_path: "models/best.pt"
confidence_threshold: 0.2
burn_radius: 35

# Timing Parameters
burn_time: 1.2        # seconds to keep laser on
servo_move_time: 0.3  # seconds to wait for servo movement
publish_rate: 2.0     # Hz for status publishing

# Paths
calibration_dir: "calibration"
