<launch>
  <!-- Weed Burner Node -->
  <node name="weed_burner" pkg="weednix_sensors" type="burn_weeds_ros.py" output="screen">
    <!-- Parameters -->
    <param name="model_path" value="$(find weednix_sensors)/models/best.pt" />
    <param name="arduino_port" value="/dev/ttyACM0" />
    <param name="camera_width" value="640" />
    <param name="camera_height" value="480" />
    <param name="burn_radius" value="35" />
    <param name="confidence_threshold" value="0.2" />
    <param name="burn_time" value="1.2" />
    <param name="servo_move_time" value="0.3" />
    <param name="publish_rate" value="2.0" />
    <param name="calibration_dir" value="$(find weednix_sensors)/calibration" />
  </node>

</launch>
