#!/usr/bin/env python3

import rospy
from weednix_sensors.msg import WeedingStatus

from ultralytics import YOLO
import cv2
import freenect
import numpy as np
import time
import serial
import json
import glob
import os
from scipy.interpolate import griddata

class ArduinoController:
    """Serial communication controller for Arduino weeding system"""

    def __init__(self, port='/dev/ttyACM0', baudrate=57600, timeout=2.0):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_conn = None
        self.connected = False

    def connect(self):
        """Connect to Arduino"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                write_timeout=self.timeout
            )

            # Wait for Arduino to initialize
            time.sleep(2.0)

            # Clear any initial messages
            self.serial_conn.flushInput()
            self.serial_conn.flushOutput()

            # Send initialization command
            response = self.send_command("INIT")
            if response and "OK" in response:
                self.connected = True
                rospy.loginfo(f"Arduino connected successfully on {self.port}")
                return True
            else:
                rospy.logerr(f"Arduino initialization failed: {response}")
                return False

        except Exception as e:
            rospy.logerr(f"Failed to connect to Arduino: {e}")
            return False

    def send_command(self, command):
        """Send command to Arduino and get response"""
        if not self.connected or not self.serial_conn:
            return None

        try:
            # Send command
            self.serial_conn.write((command + '\n').encode())
            self.serial_conn.flush()

            # Read response
            response = self.serial_conn.readline().decode().strip()
            return response

        except Exception as e:
            rospy.logwarn(f"Arduino communication error: {e}")
            return None

    def set_servo_x(self, angle):
        """Set servo X position"""
        return self.send_command(f"SX{int(angle)}")

    def set_servo_y(self, angle):
        """Set servo Y position"""
        return self.send_command(f"SY{int(angle)}")

    def set_laser(self, state):
        """Set laser state (True=ON, False=OFF)"""
        return self.send_command(f"L{1 if state else 0}")

    def get_status(self):
        """Get Arduino status"""
        return self.send_command("STATUS")

    def disconnect(self):
        """Disconnect from Arduino"""
        if self.serial_conn:
            try:
                self.set_laser(False)  # Turn off laser
                self.send_command("SX88")  # Center servos
                self.send_command("SY85")
                time.sleep(0.5)
                self.serial_conn.close()
            except:
                pass
        self.connected = False

class ROSWeedBurner:
    def __init__(self):
        # Initialize ROS node
        rospy.init_node('weed_burner_node', anonymous=True)
        
        # Create publisher for weeding status
        self.status_pub = rospy.Publisher('/weeding_status', WeedingStatus, queue_size=10)
        
        # Status tracking variables
        self.session_weeds_detected = 0
        self.session_weeds_burned = 0
        
        # Publishing rate (Hz)
        self.publish_rate = rospy.get_param('~publish_rate', 2.0)  # Default 2 Hz
        self.rate = rospy.Rate(self.publish_rate)
        
        # Load YOLO model
        model_path = rospy.get_param('~model_path', 'best.pt')
        self.model = YOLO(model_path)
        
        # Arduino setup with new controller
        arduino_port = rospy.get_param('~arduino_port', '/dev/ttyACM0')
        self.arduino = ArduinoController(port=arduino_port)

        if not self.arduino.connect():
            rospy.logerr("Failed to connect to Arduino - system cannot start")
            return
        
        # Camera dimensions
        self.CAMERA_WIDTH = rospy.get_param('~camera_width', 640)
        self.CAMERA_HEIGHT = rospy.get_param('~camera_height', 480)
        
        # Servo ranges (will be loaded from calibration)
        self.SERVO_X_MIN = 70
        self.SERVO_X_MAX = 120
        self.SERVO_Y_MIN = 70
        self.SERVO_Y_MAX = 122
        
        # Simple position-based tracking
        self.burned_positions = []  # List of burned positions
        self.BURN_RADIUS = rospy.get_param('~burn_radius', 35)  # Don't burn within this radius
        
        # Calibration data
        self.calibration_data = None
        self.interpolator_x = None
        self.interpolator_y = None
        
        # Detection parameters
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.2)
        self.burn_time = rospy.get_param('~burn_time', 1.2)  # seconds
        self.servo_move_time = rospy.get_param('~servo_move_time', 0.3)  # seconds
        
        # Initialize camera
        self.initialize_camera()

        rospy.loginfo("ROS Weed Burner initialized")

    def initialize_camera(self):
        """Initialize Kinect camera with warm-up sequence"""
        rospy.loginfo("Initializing Kinect camera...")

        # Try to get a few frames to warm up the camera
        for i in range(5):
            try:
                rospy.loginfo(f"Camera warm-up attempt {i+1}/5")
                _, _ = freenect.sync_get_depth()
                video, _ = freenect.sync_get_video()

                if video is not None:
                    rospy.loginfo(f"Camera warm-up successful on attempt {i+1}")
                    time.sleep(0.5)  # Give camera time to stabilize
                    return True

            except Exception as e:
                rospy.logwarn(f"Camera warm-up attempt {i+1} failed: {e}")
                time.sleep(1.0)

        rospy.logwarn("Camera warm-up completed with some failures - continuing anyway")
        return False

    def get_kinect_frame(self):
        """Capture frame from Kinect v1 with retry logic"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # Try to get depth first to initialize camera
                _, _ = freenect.sync_get_depth()
                video, _ = freenect.sync_get_video()

                if video is not None:
                    frame = cv2.cvtColor(video, cv2.COLOR_RGB2BGR)
                    if frame is not None and frame.size > 0:
                        return frame

                rospy.logwarn(f"Kinect frame attempt {attempt + 1} returned empty frame")
                time.sleep(0.5)  # Wait before retry

            except Exception as e:
                rospy.logwarn(f"Kinect frame attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(1.0)  # Wait longer between retries

        rospy.logerr("Failed to get Kinect frame after all retries")
        return None

    def load_calibration(self):
        """Load the most recent calibration file"""
        calibration_dir = rospy.get_param('~calibration_dir', '.')
        files = glob.glob(os.path.join(calibration_dir, "servo_calibration_*.json"))
        
        if not files:
            rospy.logwarn("No calibration files found! Creating fallback...")
            basic_calibration = {
                "timestamp": "fallback",
                "servo_ranges": {"x_min": self.SERVO_X_MIN, "x_max": self.SERVO_X_MAX, 
                               "y_min": self.SERVO_Y_MIN, "y_max": self.SERVO_Y_MAX},
                "camera_dimensions": {"width": self.CAMERA_WIDTH, "height": self.CAMERA_HEIGHT},
                "calibration_points": [
                    {"camera": [136, 234], "servo": [76, 92]},
                    {"camera": [320, 240], "servo": [88, 85]},
                    {"camera": [500, 150], "servo": [100, 78]}
                ]
            }
            fallback_path = os.path.join(calibration_dir, "servo_calibration_fallback.json")
            with open(fallback_path, 'w') as f:
                json.dump(basic_calibration, f, indent=2)
            files = [fallback_path]

        latest_file = max(files, key=os.path.getctime)
        try:
            with open(latest_file, 'r') as f:
                self.calibration_data = json.load(f)

            rospy.loginfo(f"Loaded calibration from: {latest_file}")

            # Update servo ranges
            ranges = self.calibration_data['servo_ranges']
            self.SERVO_X_MIN = ranges['x_min']
            self.SERVO_X_MAX = ranges['x_max']
            self.SERVO_Y_MIN = ranges['y_min']
            self.SERVO_Y_MAX = ranges['y_max']

            # Create interpolators
            points = self.calibration_data['calibration_points']
            camera_coords = np.array([point['camera'] for point in points])
            servo_x_coords = np.array([point['servo'][0] for point in points])
            servo_y_coords = np.array([point['servo'][1] for point in points])

            def safe_interpolator_x(x, y):
                try:
                    if len(points) >= 3:
                        result = griddata(camera_coords, servo_x_coords, (x, y), method='linear', fill_value=np.nan)
                        if np.isnan(result):
                            result = griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
                        return result
                    else:
                        return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
                except:
                    return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')

            def safe_interpolator_y(x, y):
                try:
                    if len(points) >= 3:
                        result = griddata(camera_coords, servo_y_coords, (x, y), method='linear', fill_value=np.nan)
                        if np.isnan(result):
                            result = griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
                        return result
                    else:
                        return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
                except:
                    return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')

            self.interpolator_x = safe_interpolator_x
            self.interpolator_y = safe_interpolator_y
            return True

        except Exception as e:
            rospy.logerr(f"Error loading calibration: {e}")
            return False

    def camera_to_servo_angles(self, camera_x, camera_y):
        """Convert camera coordinates to servo angles"""
        if self.interpolator_x is None or self.interpolator_y is None:
            return 88, 85

        try:
            servo_x = self.interpolator_x(camera_x, camera_y)
            servo_y = self.interpolator_y(camera_x, camera_y)

            if servo_x is None or servo_y is None or np.isnan(servo_x) or np.isnan(servo_y):
                return 88, 85

            servo_x = max(self.SERVO_X_MIN, min(self.SERVO_X_MAX, float(servo_x)))
            servo_y = max(self.SERVO_Y_MIN, min(self.SERVO_Y_MAX, float(servo_y)))
            return int(servo_x), int(servo_y)
        except:
            return 88, 85

    def compute_center(self, box):
        """Compute center of bounding box"""
        x1, y1, x2, y2 = box
        return [int((x1 + x2) / 2), int((y1 + y2) / 2)]

    def is_near_burned_position(self, center, burned_positions, radius=35):
        """Check if position is near a previously burned position"""
        for burned_pos in burned_positions:
            distance = np.linalg.norm(np.array(center) - np.array(burned_pos))
            if distance < radius:
                return True, distance
        return False, 0

    def try_tracking(self, frame, conf=0.25):
        """Use ByteTrack only for tracking as requested"""
        try:
            # Use ByteTrack only as requested by user
            results = self.model.track(frame, conf=conf, persist=True, tracker="bytetrack.yaml")
            return results, "ByteTrack"
        except Exception as e:
            rospy.logdebug(f"ByteTrack failed: {e}")

            # Fallback to detection only if ByteTrack fails
            try:
                results = self.model(frame, conf=conf, stream=True)
                return results, "Detection Only"
            except Exception as e:
                rospy.logwarn(f"Detection failed: {e}")
                return None, "Failed"

    def publish_status(self):
        """Publish current weeding status"""
        msg = WeedingStatus()
        msg.weeds_detected = self.session_weeds_detected
        msg.weeds_burned = self.session_weeds_burned
        msg.stamp = rospy.Time.now()
        
        self.status_pub.publish(msg)
        rospy.logdebug(f"Published status: Detected={self.session_weeds_detected}, Burned={self.session_weeds_burned}")

    def run(self):
        """Main weed burning loop - ROS integrated version"""
        rospy.loginfo("🔥 ROS WEED BURNING SYSTEM STARTING")
        rospy.loginfo("=" * 50)

        if not self.load_calibration():
            rospy.logerr("Cannot start without calibration data!")
            return

        # Initialize servos to center
        self.arduino.set_servo_x(88)
        self.arduino.set_servo_y(85)
        time.sleep(1)

        rospy.loginfo("🎯 Starting weed detection and burning...")
        rospy.loginfo("🔧 Publishing status on /weeding_status topic")

        frame_count = 0
        last_status_time = rospy.Time.now()
        status_publish_interval = rospy.Duration(1.0 / self.publish_rate)
        failed_frame_count = 0
        max_failed_frames = 10

        try:
            while not rospy.is_shutdown():
                frame_start_time = time.time()

                frame = self.get_kinect_frame()
                if frame is None:
                    failed_frame_count += 1
                    if failed_frame_count >= max_failed_frames:
                        rospy.logerr(f"Too many failed frames ({failed_frame_count}), camera may be disconnected")
                        break
                    rospy.logwarn(f"Failed to get frame ({failed_frame_count}/{max_failed_frames}), skipping...")
                    time.sleep(0.1)  # Short delay before retry
                    continue

                # Reset failed frame counter on success
                failed_frame_count = 0

                frame = cv2.resize(frame, (self.CAMERA_WIDTH, self.CAMERA_HEIGHT))
                frame_count += 1

                rospy.logdebug(f"--- Frame {frame_count} ---")

                # Try tracking/detection
                results, method = self.try_tracking(frame, conf=0.25)
                
                if results is None:
                    rospy.logwarn("No detection method working, skipping frame")
                    continue
                    
                rospy.logdebug(f"Detection method: {method}")

                # Process all detections
                frame_detections = 0
                frame_burns = 0

                for result in results:
                    boxes = result.boxes
                    if boxes is None:
                        continue

                    frame_detections += len(boxes)
                    rospy.logdebug(f"Found {len(boxes)} detections")

                    for i, box in enumerate(boxes):
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        confidence = float(box.conf[0])
                        center = self.compute_center((x1, y1, x2, y2))

                        rospy.logdebug(f"  Detection {i}: Center=({center[0]}, {center[1]}), Conf={confidence:.2f}")

                        # Check if this position was already burned
                        is_burned, distance = self.is_near_burned_position(center, self.burned_positions, self.BURN_RADIUS)
                        
                        if is_burned:
                            rospy.logdebug(f"    Skipping - too close to burned position (distance: {distance:.1f})")
                        else:
                            # BURN IMMEDIATELY if confidence is decent
                            if confidence > self.confidence_threshold:
                                rospy.loginfo(f"🔥 BURNING NEW WEED - Conf: {confidence:.2f}")
                                
                                # Get servo angles
                                servo_angle_x, servo_angle_y = self.camera_to_servo_angles(center[0], center[1])
                                
                                # Move and burn
                                self.arduino.set_servo_x(servo_angle_x)
                                self.arduino.set_servo_y(servo_angle_y)
                                time.sleep(self.servo_move_time)

                                self.arduino.set_laser(True)
                                rospy.loginfo(f"🎯 LASER ON at ({servo_angle_x}°, {servo_angle_y}°)")
                                time.sleep(self.burn_time)
                                self.arduino.set_laser(False)
                                rospy.loginfo(f"🔥 LASER OFF - Position burned!")
                                
                                # Remember this position
                                self.burned_positions.append(center)
                                frame_burns += 1
                            else:
                                rospy.logdebug(f"    Confidence too low: {confidence:.2f}")

                # Update session counters
                self.session_weeds_detected += frame_detections
                self.session_weeds_burned += frame_burns

                # Publish status at specified rate
                current_time = rospy.Time.now()
                if current_time - last_status_time >= status_publish_interval:
                    self.publish_status()
                    last_status_time = current_time

                # Calculate frame processing time
                frame_end_time = time.time()
                frame_processing_time = frame_end_time - frame_start_time
                fps = 1.0 / frame_processing_time if frame_processing_time > 0 else 0

                # Print frame summary with performance info
                rospy.loginfo(f"Frame {frame_count}: {frame_detections} detected, {frame_burns} burned | Session total: {self.session_weeds_detected} detected, {self.session_weeds_burned} burned | FPS: {fps:.1f}")

                # Small delay to prevent system overload
                time.sleep(0.01)

        except rospy.ROSInterruptException:
            rospy.loginfo("ROS shutdown requested")
        except KeyboardInterrupt:
            rospy.loginfo("Keyboard interrupt received")
        except Exception as e:
            rospy.logerr(f"Unexpected error: {e}")

        finally:
            self.shutdown()

    def shutdown(self):
        """Clean shutdown procedure"""
        rospy.loginfo("🛑 Shutting down weed burner...")
        
        # Publish final status
        self.publish_status()
        
        # Safe hardware shutdown
        try:
            self.arduino.disconnect()
        except Exception as e:
            rospy.logwarn(f"Error during hardware shutdown: {e}")
        
        cv2.destroyAllWindows()
        rospy.loginfo("✅ System shutdown complete")

if __name__ == "__main__":
    try:
        weed_burner = ROSWeedBurner()
        weed_burner.run()
    except rospy.ROSInterruptException:
        pass