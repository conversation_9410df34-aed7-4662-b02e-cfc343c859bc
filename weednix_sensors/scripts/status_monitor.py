# File: scripts/status_monitor.py
#!/usr/bin/env python3
"""
Simple status monitor for the weeding system
Subscribes to /weeding_status and prints updates
"""

import rospy
from weednix_sensors.msg import WeedingStatus

class WeedingStatusMonitor:
    def __init__(self):
        rospy.init_node('weeding_status_monitor', anonymous=True)
        
        # Subscribe to weeding status
        self.status_sub = rospy.Subscriber('/weeding_status', WeedingStatus, self.status_callback)
        
        rospy.loginfo("Weeding Status Monitor started")
        rospy.loginfo("Listening for status updates on /weeding_status...")
        
    def status_callback(self, msg):
        """Handle incoming status messages"""
        rospy.loginfo(f"🌿 Weeding Status Update:")
        rospy.loginfo(f"   Weeds Detected: {msg.weeds_detected}")
        rospy.loginfo(f"   Weeds Burned: {msg.weeds_burned}")
        rospy.loginfo(f"   Timestamp: {msg.stamp.secs}.{msg.stamp.nsecs}")
        
        if msg.weeds_detected > 0:
            success_rate = (msg.weeds_burned / msg.weeds_detected) * 100
            rospy.loginfo(f"   Success Rate: {success_rate:.1f}%")
        
        rospy.loginfo("-" * 40)

if __name__ == "__main__":
    try:
        monitor = WeedingStatusMonitor()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
