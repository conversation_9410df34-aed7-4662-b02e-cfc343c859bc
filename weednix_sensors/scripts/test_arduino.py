#!/usr/bin/env python3

"""
Arduino Connection Test Script
This script tests the connection to the Arduino weeding system.
"""

import serial
import time
import sys

def test_arduino_connection(port='/dev/ttyACM0', baudrate=57600):
    """Test Arduino connection and communication"""
    print(f"🔧 Testing Arduino connection on {port} at {baudrate} baud...")
    
    try:
        # Try to connect
        ser = serial.Serial(port=port, baudrate=baudrate, timeout=2.0)
        print(f"✅ Serial port opened successfully")
        
        # Wait for <PERSON>rduin<PERSON> to initialize
        print("⏳ Waiting for Arduino to initialize...")
        time.sleep(3.0)
        
        # Clear any initial messages
        ser.flushInput()
        ser.flushOutput()
        
        # Send INIT command
        print("📤 Sending INIT command...")
        ser.write(b'INIT\n')
        ser.flush()
        
        # Read response
        response = ser.readline().decode().strip()
        print(f"📥 Response: {response}")
        
        if "OK" in response:
            print("✅ Arduino initialization successful!")
            
            # Test servo commands
            print("\n🔧 Testing servo commands...")
            
            # Test servo X
            ser.write(b'SX88\n')
            ser.flush()
            response = ser.readline().decode().strip()
            print(f"📥 Servo X response: {response}")
            
            # Test servo Y
            ser.write(b'SY85\n')
            ser.flush()
            response = ser.readline().decode().strip()
            print(f"📥 Servo Y response: {response}")
            
            # Test laser OFF (safety)
            ser.write(b'L0\n')
            ser.flush()
            response = ser.readline().decode().strip()
            print(f"📥 Laser OFF response: {response}")
            
            # Get status
            ser.write(b'STATUS\n')
            ser.flush()
            response = ser.readline().decode().strip()
            print(f"📥 Status response: {response}")
            
            print("\n✅ All tests passed! Arduino is working correctly.")
            
        else:
            print(f"❌ Arduino initialization failed: {response}")
            return False
            
        ser.close()
        return True
        
    except serial.SerialException as e:
        print(f"❌ Serial connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_available_ports():
    """Check available serial ports"""
    import glob
    
    print("\n🔍 Checking available serial ports...")
    
    # Common Arduino port patterns
    patterns = ['/dev/ttyACM*', '/dev/ttyUSB*', '/dev/ttyS*']
    
    available_ports = []
    for pattern in patterns:
        ports = glob.glob(pattern)
        available_ports.extend(ports)
    
    if available_ports:
        print("📋 Available serial ports:")
        for port in available_ports:
            print(f"   - {port}")
    else:
        print("❌ No serial ports found")
        print("💡 Make sure Arduino is connected via USB")
    
    return available_ports

if __name__ == "__main__":
    print("🤖 Arduino Weeding System Connection Test")
    print("=" * 50)
    
    # Check available ports first
    available_ports = check_available_ports()
    
    # Test default port
    port = '/dev/ttyACM0'
    if len(sys.argv) > 1:
        port = sys.argv[1]
    
    print(f"\n🎯 Testing port: {port}")
    
    if port not in available_ports and available_ports:
        print(f"⚠️  Warning: {port} not found in available ports")
        print(f"💡 Try one of these ports: {', '.join(available_ports)}")
        
        if available_ports:
            print(f"\n🔄 Trying first available port: {available_ports[0]}")
            port = available_ports[0]
    
    success = test_arduino_connection(port)
    
    if success:
        print(f"\n🎉 Arduino test completed successfully!")
        print(f"💡 You can now run the weeding system with: roslaunch weednix_sensors weed_burner.launch")
    else:
        print(f"\n❌ Arduino test failed!")
        print(f"💡 Troubleshooting steps:")
        print(f"   1. Check Arduino is connected via USB")
        print(f"   2. Check the correct port (try: ls /dev/tty*)")
        print(f"   3. Make sure Arduino code is uploaded")
        print(f"   4. Check user permissions (add to dialout group)")
        print(f"   5. Try different baud rates (9600, 115200)")
