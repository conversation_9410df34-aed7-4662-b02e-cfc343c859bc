#!/usr/bin/env python3

"""
Test script for weeding status topic
This script publishes test data to the /weeding_status topic to verify it's working correctly.
"""

import rospy
from weednix_sensors.msg import WeedingStatus
import time

class WeedingStatusTester:
    def __init__(self):
        rospy.init_node('weeding_status_tester', anonymous=True)
        
        # Create publisher for weeding status
        self.status_pub = rospy.Publisher('/weeding_status', WeedingStatus, queue_size=10)
        
        # Wait for publisher to be ready
        rospy.sleep(1.0)
        
        rospy.loginfo("🧪 Weeding Status Tester Started")
        rospy.loginfo("📡 Publishing test data to /weeding_status topic...")
        
    def publish_test_data(self):
        """Publish test weeding status data"""
        rate = rospy.Rate(1)  # 1 Hz
        
        weeds_detected = 0
        weeds_burned = 0
        
        try:
            while not rospy.is_shutdown():
                # Create test message
                msg = WeedingStatus()
                
                # Simulate detection and burning
                if weeds_detected < 50:  # Simulate up to 50 detections
                    weeds_detected += 1
                    
                    # Simulate burning with 80% success rate
                    if weeds_detected % 5 != 0:  # Skip every 5th weed to simulate missed burns
                        weeds_burned += 1
                
                msg.weeds_detected = weeds_detected
                msg.weeds_burned = weeds_burned
                msg.stamp = rospy.Time.now()
                
                # Publish the message
                self.status_pub.publish(msg)
                
                # Calculate efficiency
                efficiency = (weeds_burned / weeds_detected * 100) if weeds_detected > 0 else 0
                
                rospy.loginfo(f"📊 Published: Detected={weeds_detected}, Burned={weeds_burned}, Efficiency={efficiency:.1f}%")
                
                rate.sleep()
                
        except KeyboardInterrupt:
            rospy.loginfo("🛑 Test stopped by user")
        except rospy.ROSInterruptException:
            rospy.loginfo("🛑 ROS shutdown requested")

if __name__ == "__main__":
    try:
        tester = WeedingStatusTester()
        tester.publish_test_data()
    except Exception as e:
        rospy.logerr(f"❌ Test error: {e}")
