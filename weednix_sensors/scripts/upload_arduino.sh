#!/bin/bash

# Arduino Upload Script for Weeding System
# This script uploads the weeding system Arduino code to the Arduino board
# Run this script ONCE to upload the code, then use the ROS system for communication

set -e  # Exit on any error

# Configuration
ARDUINO_CODE_DIR="$(dirname "$0")/../arduino_code"
SKETCH_FILE="weeding_system.ino"
ARDUINO_PORT="/dev/ttyACM0"
BOARD_TYPE="arduino:avr:uno"  # Change this if using a different board

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  Arduino Weeding System Upload Script${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# Check if arduino-cli is installed
if ! command -v arduino-cli &> /dev/null; then
    echo -e "${RED}Error: arduino-cli is not installed${NC}"
    echo -e "${YELLOW}Please install arduino-cli:${NC}"
    echo "  curl -fsSL https://raw.githubusercontent.com/arduino/arduino-cli/master/install.sh | sh"
    echo "  sudo mv bin/arduino-cli /usr/local/bin/"
    echo "  arduino-cli core update-index"
    echo "  arduino-cli core install arduino:avr"
    exit 1
fi

# Check if sketch file exists
SKETCH_PATH="$ARDUINO_CODE_DIR/$SKETCH_FILE"
if [ ! -f "$SKETCH_PATH" ]; then
    echo -e "${RED}Error: Sketch file not found at $SKETCH_PATH${NC}"
    exit 1
fi

echo -e "${BLUE}Configuration:${NC}"
echo -e "  Sketch: ${SKETCH_PATH}"
echo -e "  Port: ${ARDUINO_PORT}"
echo -e "  Board: ${BOARD_TYPE}"
echo

# Check if Arduino is connected
if [ ! -e "$ARDUINO_PORT" ]; then
    echo -e "${RED}Error: Arduino not found at $ARDUINO_PORT${NC}"
    echo -e "${YELLOW}Available serial devices:${NC}"
    ls -la /dev/tty* | grep -E "(ACM|USB)" || echo "  No Arduino devices found"
    echo
    echo -e "${YELLOW}Please check:${NC}"
    echo "  1. Arduino is connected via USB"
    echo "  2. Arduino port is correct (check with 'dmesg | tail' after plugging in)"
    echo "  3. User has permission to access serial ports (add user to dialout group)"
    exit 1
fi

echo -e "${YELLOW}Checking Arduino connection...${NC}"
arduino-cli board list

echo
echo -e "${YELLOW}Compiling sketch...${NC}"
arduino-cli compile --fqbn "$BOARD_TYPE" "$ARDUINO_CODE_DIR"

echo
echo -e "${YELLOW}Uploading to Arduino...${NC}"
arduino-cli upload -p "$ARDUINO_PORT" --fqbn "$BOARD_TYPE" "$ARDUINO_CODE_DIR"

echo
echo -e "${GREEN}✅ Upload completed successfully!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "  1. The Arduino is now programmed with the weeding system code"
echo "  2. You can now run the ROS weeding system:"
echo "     roslaunch weednix_sensors weed_burner.launch"
echo "  3. Monitor the system with:"
echo "     rosrun weednix_sensors weeding_status_monitor.py"
echo
echo -e "${YELLOW}Note: You only need to upload this code ONCE unless you make changes${NC}"
echo -e "${YELLOW}The ROS system will communicate with the Arduino via serial commands${NC}"
echo
