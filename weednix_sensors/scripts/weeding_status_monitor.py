#!/usr/bin/env python3

"""
Weeding Status Monitor
This script subscribes to the /weeding_status topic and displays real-time weeding statistics.
Useful for testing and monitoring the weed burning system.
"""

import rospy
from weednix_sensors.msg import WeedingStatus
import time

class WeedingStatusMonitor:
    def __init__(self):
        rospy.init_node('weeding_status_monitor', anonymous=True)
        
        # Subscribe to weeding status topic
        self.status_sub = rospy.Subscriber('/weeding_status', WeedingStatus, self.status_callback)
        
        # Statistics tracking
        self.last_detected = 0
        self.last_burned = 0
        self.start_time = time.time()
        self.last_update_time = None
        
        rospy.loginfo("📊 Weeding Status Monitor Started")
        rospy.loginfo("🔍 Listening to /weeding_status topic...")
        
    def status_callback(self, msg):
        """Callback function for weeding status messages"""
        current_time = time.time()
        
        # Calculate rates
        if self.last_update_time is not None:
            time_diff = current_time - self.last_update_time
            detection_rate = (msg.weeds_detected - self.last_detected) / time_diff if time_diff > 0 else 0
            burning_rate = (msg.weeds_burned - self.last_burned) / time_diff if time_diff > 0 else 0
        else:
            detection_rate = 0
            burning_rate = 0
        
        # Calculate session statistics
        session_time = current_time - self.start_time
        avg_detection_rate = msg.weeds_detected / session_time if session_time > 0 else 0
        avg_burning_rate = msg.weeds_burned / session_time if session_time > 0 else 0
        
        # Calculate efficiency
        efficiency = (msg.weeds_burned / msg.weeds_detected * 100) if msg.weeds_detected > 0 else 0
        
        # Display status
        rospy.loginfo("=" * 60)
        rospy.loginfo("🔥 WEEDING SYSTEM STATUS")
        rospy.loginfo("=" * 60)
        rospy.loginfo(f"📊 Current Session:")
        rospy.loginfo(f"   🎯 Weeds Detected: {msg.weeds_detected}")
        rospy.loginfo(f"   🔥 Weeds Burned:   {msg.weeds_burned}")
        rospy.loginfo(f"   ⚡ Efficiency:     {efficiency:.1f}%")
        rospy.loginfo(f"")
        rospy.loginfo(f"📈 Rates (per second):")
        rospy.loginfo(f"   🔍 Detection Rate: {detection_rate:.2f}")
        rospy.loginfo(f"   🔥 Burning Rate:   {burning_rate:.2f}")
        rospy.loginfo(f"")
        rospy.loginfo(f"📊 Session Averages:")
        rospy.loginfo(f"   🔍 Avg Detection:  {avg_detection_rate:.2f}/sec")
        rospy.loginfo(f"   🔥 Avg Burning:    {avg_burning_rate:.2f}/sec")
        rospy.loginfo(f"   ⏱️  Session Time:   {session_time:.1f} seconds")
        rospy.loginfo(f"")
        rospy.loginfo(f"🕐 Last Update: {rospy.Time.now()}")
        rospy.loginfo("=" * 60)
        
        # Update tracking variables
        self.last_detected = msg.weeds_detected
        self.last_burned = msg.weeds_burned
        self.last_update_time = current_time
    
    def run(self):
        """Keep the monitor running"""
        rospy.loginfo("🚀 Monitor is running. Press Ctrl+C to stop.")
        try:
            rospy.spin()
        except KeyboardInterrupt:
            rospy.loginfo("🛑 Monitor stopped by user")
        except rospy.ROSInterruptException:
            rospy.loginfo("🛑 ROS shutdown requested")

if __name__ == "__main__":
    try:
        monitor = WeedingStatusMonitor()
        monitor.run()
    except Exception as e:
        rospy.logerr(f"❌ Monitor error: {e}")
